import { 
  pgTable, 
  text, 
  timestamp, 
  uuid, 
  integer, 
  boolean, 
  date, 
  jsonb, 
  serial,
  check,
  index,
  pgEnum,
  varchar,
  unique,
  foreignKey,
  pgPolicy,
  uniqueIndex
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';

// Enums
export const signupEmailDomainType = pgEnum('signup_email_domain_type', ['allow', 'deny']);

// Signup Email Domains table
export const signupEmailDomains = pgTable('signup_email_domains', {
  id: serial().primaryKey().notNull(),
  domain: text().notNull(),
  type: signupEmailDomainType().notNull(),
  reason: text(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
  unique('signup_email_domains_domain_key').on(table.domain),
]);

// Users table
export const users = pgTable('users', {
  id: uuid().defaultRandom().primaryKey().notNull(),
  email: text(),
  fullName: text('full_name'),
  role: text().default('employee').notNull(),
  startDate: date('start_date'),
  endDate: date('end_date'),
  gender: text(),
  position: text(),
  phone: text(),
  dateOfBirth: date('date_of_birth'),
  isActive: boolean('is_active').default(true),
  managerId: uuid('manager_id'),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }).defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
  foreignKey({
    columns: [table.managerId],
    foreignColumns: [table.id],
    name: 'users_manager_id_fkey'
  }),
  unique('users_email_key').on(table.email),
  pgPolicy('Admins can perform all actions on users', { 
    as: 'permissive', 
    for: 'all', 
    to: ['public'], 
    using: sql`(get_user_role(auth.uid()) = 'admin'::text)`, 
    withCheck: sql`(get_user_role(auth.uid()) = 'admin'::text)`  
  }),
  pgPolicy('Authenticated users can view other users', { 
    as: 'permissive', 
    for: 'select', 
    to: ['authenticated'] 
  }),
  pgPolicy('Users can update their own profile', { 
    as: 'permissive', 
    for: 'update', 
    to: ['authenticated'] 
  }),
  check('users_role_check', sql`role = ANY (ARRAY['employee'::text, 'manager'::text, 'admin'::text])`),
]);

// Addresses table
export const addresses = pgTable('addresses', {
  id: uuid().defaultRandom().primaryKey().notNull(),
  userId: uuid('user_id'),
  addressLine: text('address_line').notNull(),
  city: text(),
  state: text(),
  postalCode: text('postal_code'),
  country: text(),
  type: text(),
  isPrimary: boolean('is_primary').default(false),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }).defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
  uniqueIndex('one_primary_address_per_user').using('btree', table.userId.asc().nullsLast().op('uuid_ops')).where(sql`(is_primary = true)`),
  foreignKey({
    columns: [table.userId],
    foreignColumns: [users.id],
    name: 'addresses_user_id_fkey'
  }).onDelete('cascade'),
]);

// Projects table
export const projects = pgTable('projects', {
  id: uuid().defaultRandom().primaryKey().notNull(),
  name: text().notNull(),
  isActive: boolean('is_active').default(true).notNull(),
  isBillable: boolean('is_billable').default(true).notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }).defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
  unique('projects_name_key').on(table.name),
  pgPolicy('Anyone can view projects', { 
    as: 'permissive', 
    for: 'select', 
    to: ['public'], 
    using: sql`true` 
  }),
  pgPolicy('Admins can manage projects', { 
    as: 'permissive', 
    for: 'all', 
    to: ['public'] 
  }),
]);

// Project Assignments table
export const projectAssignments = pgTable('project_assignments', {
  id: uuid().defaultRandom().primaryKey().notNull(),
  userId: uuid('user_id').notNull(),
  projectId: uuid('project_id').notNull(),
  role: varchar({ length: 50 }).default('developer').notNull(),
  isLead: boolean('is_lead').default(false).notNull(),
  startDate: date('start_date').default(sql`CURRENT_DATE`).notNull(),
  endDate: date('end_date'),
  status: varchar({ length: 20 }).default('active').notNull(),
  assignedBy: uuid('assigned_by'),
  assignedAt: timestamp('assigned_at', { withTimezone: true, mode: 'string' }).defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
  foreignKey({
    columns: [table.userId],
    foreignColumns: [users.id],
    name: 'project_assignments_user_id_fkey'
  }).onDelete('cascade'),
  foreignKey({
    columns: [table.projectId],
    foreignColumns: [projects.id],
    name: 'project_assignments_project_id_fkey'
  }).onDelete('cascade'),
  foreignKey({
    columns: [table.assignedBy],
    foreignColumns: [users.id],
    name: 'project_assignments_assigned_by_fkey'
  }),
  pgPolicy('admin_crud_project_assignments', { 
    as: 'permissive', 
    for: 'all', 
    to: ['public'], 
    using: sql`(EXISTS ( SELECT 1
   FROM users u
  WHERE ((u.id = auth.uid()) AND (u.role = 'admin'::text))))`, 
    withCheck: sql`(EXISTS ( SELECT 1
   FROM users u
  WHERE ((u.id = auth.uid()) AND (u.role = 'admin'::text))))`  
  }),
  pgPolicy('user_crud_own_assignments', { 
    as: 'permissive', 
    for: 'all', 
    to: ['public'] 
  }),
  check('valid_date_range', sql`(end_date IS NULL) OR (end_date >= start_date)`),
]);

// Company Settings table
export const companySettings = pgTable('company_settings', {
  id: serial().primaryKey().notNull(),
  carryoverExpiryDay: integer('carryover_expiry_day').notNull(),
  carryoverExpiryMonth: integer('carryover_expiry_month').notNull(),
  tenureAccrualRules: jsonb('tenure_accrual_rules').notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, () => [
  pgPolicy('Allow read to all authenticated users', { 
    as: 'permissive', 
    for: 'select', 
    to: ['public'], 
    using: sql`(auth.role() = 'authenticated'::text)` 
  }),
  pgPolicy('Admins can modify company_settings', { 
    as: 'permissive', 
    for: 'all', 
    to: ['public'] 
  }),
  pgPolicy('Service role can modify', { 
    as: 'permissive', 
    for: 'all', 
    to: ['public'] 
  }),
]);

// Leave Types table
export const leaveTypes = pgTable('leave_types', {
  id: serial().primaryKey().notNull(),
  name: text().notNull(),
  description: text(),
  isPaid: boolean('is_paid').default(true).notNull(),
  supportsCarryover: boolean('supports_carryover').default(false).notNull(),
  supportsHalfDay: boolean('supports_half_day').default(false).notNull(),
  quota: integer(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
  unique('leave_types_name_key').on(table.name),
  pgPolicy('Allow read to all authenticated users', { 
    as: 'permissive', 
    for: 'select', 
    to: ['public'], 
    using: sql`(auth.role() = 'authenticated'::text)` 
  }),
  pgPolicy('Admins can modify leave_types', { 
    as: 'permissive', 
    for: 'all', 
    to: ['public'] 
  }),
  pgPolicy('Service role can modify', { 
    as: 'permissive', 
    for: 'all', 
    to: ['public'] 
  }),
]);

// Leave Requests table
export const leaveRequests = pgTable('leave_requests', {
  id: uuid().defaultRandom().primaryKey().notNull(),
  userId: uuid('user_id').notNull(),
  leaveTypeId: integer('leave_type_id').notNull(),
  projects: jsonb(),
  internalNotifications: uuid('internal_notifications').array(),
  externalNotifications: text('external_notifications').array(),
  currentManagerId: uuid('current_manager_id'),
  backupId: uuid('backup_id'),
  startDate: date('start_date').notNull(),
  endDate: date('end_date'),
  isHalfDay: boolean('is_half_day').default(false).notNull(),
  halfDayType: text('half_day_type'),
  message: text(),
  emergencyContact: text('emergency_contact'),
  status: text().notNull(),
  approvalNotes: text('approval_notes'),
  cancelReason: text('cancel_reason'),
  approvedById: uuid('approved_by_id'),
  approvedAt: timestamp('approved_at', { withTimezone: true, mode: 'string' }),
  canceledAt: timestamp('canceled_at', { withTimezone: true, mode: 'string' }),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
}, (table) => [
  index('idx_leave_requests_leave_type_id').using('btree', table.leaveTypeId.asc().nullsLast().op('int4_ops')),
  index('idx_leave_requests_start_date').using('btree', table.startDate.asc().nullsLast().op('date_ops')),
  index('idx_leave_requests_status').using('btree', table.status.asc().nullsLast().op('text_ops')),
  index('idx_leave_requests_user_id').using('btree', table.userId.asc().nullsLast().op('uuid_ops')),
  foreignKey({
    columns: [table.leaveTypeId],
    foreignColumns: [leaveTypes.id],
    name: 'leave_requests_leave_type_id_fkey'
  }),
  foreignKey({
    columns: [table.currentManagerId],
    foreignColumns: [users.id],
    name: 'leave_requests_current_manager_id_fkey'
  }),
  foreignKey({
    columns: [table.backupId],
    foreignColumns: [users.id],
    name: 'leave_requests_backup_id_fkey'
  }),
  foreignKey({
    columns: [table.approvedById],
    foreignColumns: [users.id],
    name: 'leave_requests_approved_by_id_fkey'
  }),
  foreignKey({
    columns: [table.userId],
    foreignColumns: [users.id],
    name: 'leave_requests_user_id_fkey'
  }).onUpdate('cascade').onDelete('cascade'),
  pgPolicy('Users can view their own leave requests', { 
    as: 'permissive', 
    for: 'select', 
    to: ['public'], 
    using: sql`(auth.uid() = user_id)` 
  }),
  pgPolicy('Users can insert their own leave requests', { 
    as: 'permissive', 
    for: 'insert', 
    to: ['public'] 
  }),
  pgPolicy('Service role can do everything', { 
    as: 'permissive', 
    for: 'all', 
    to: ['public'] 
  }),
  pgPolicy('Admins and managers can view and update all leave requests', { 
    as: 'permissive', 
    for: 'all', 
    to: ['public'] 
  }),
  pgPolicy('Users can update their own pending leave requests', { 
    as: 'permissive', 
    for: 'update', 
    to: ['public'] 
  }),
  check('leave_requests_half_day_type_check', sql`half_day_type = ANY (ARRAY['morning'::text, 'afternoon'::text])`),
  check('leave_requests_status_check', sql`status = ANY (ARRAY['pending'::text, 'approved'::text, 'rejected'::text, 'canceled'::text])`),
]);

// Extended Absences table
export const extendedAbsences = pgTable('extended_absences', {
  id: uuid().defaultRandom().primaryKey().notNull(),
  userId: uuid('user_id'),
  startDate: date('start_date').notNull(),
  endDate: date('end_date').notNull(),
  reason: text(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }).defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
  foreignKey({
    columns: [table.userId],
    foreignColumns: [users.id],
    name: 'extended_absences_user_id_fkey'
  }).onDelete('cascade'),
  pgPolicy('Users can view their own extended absences', { 
    as: 'permissive', 
    for: 'select', 
    to: ['public'], 
    using: sql`(auth.uid() = user_id)` 
  }),
  pgPolicy('Admins can manage all extended absences', { 
    as: 'permissive', 
    for: 'all', 
    to: ['public'] 
  }),
]);

// Bonus Leave Grants table
export const bonusLeaveGrants = pgTable('bonus_leave_grants', {
  id: uuid().defaultRandom().primaryKey().notNull(),
  userId: uuid('user_id').notNull(),
  year: integer().notNull(),
  daysGranted: integer('days_granted').notNull(),
  daysUsed: integer('days_used').default(0),
  reason: text(),
  grantedBy: uuid('granted_by'),
  grantedAt: timestamp('granted_at', { withTimezone: true, mode: 'string' }).defaultNow(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }).defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }).defaultNow(),
}, (table) => [
  index('idx_bonus_leave_grants_granted_by').using('btree', table.grantedBy.asc().nullsLast().op('uuid_ops')),
  index('idx_bonus_leave_grants_user_id').using('btree', table.userId.asc().nullsLast().op('uuid_ops')),
  index('idx_bonus_leave_grants_year').using('btree', table.year.asc().nullsLast().op('int4_ops')),
  foreignKey({
    columns: [table.userId],
    foreignColumns: [users.id],
    name: 'bonus_leave_grants_user_id_fkey'
  }).onDelete('cascade'),
  foreignKey({
    columns: [table.grantedBy],
    foreignColumns: [users.id],
    name: 'bonus_leave_grants_granted_by_fkey'
  }),
  pgPolicy('Users can view own bonus leave grants', { 
    as: 'permissive', 
    for: 'select', 
    to: ['public'], 
    using: sql`(auth.uid() = user_id)` 
  }),
  pgPolicy('Admins can view all bonus leave grants', { 
    as: 'permissive', 
    for: 'select', 
    to: ['public'] 
  }),
  pgPolicy('Admins can insert bonus leave grants', { 
    as: 'permissive', 
    for: 'insert', 
    to: ['public'] 
  }),
  pgPolicy('Admins can update bonus leave grants', { 
    as: 'permissive', 
    for: 'update', 
    to: ['public'] 
  }),
  pgPolicy('Admins can delete bonus leave grants', { 
    as: 'permissive', 
    for: 'delete', 
    to: ['public'] 
  }),
  check('bonus_leave_grants_year_check', sql`(year >= 2020) AND (year <= 2030)`),
  check('bonus_leave_grants_days_granted_check', sql`days_granted > 0`),
  check('bonus_leave_grants_days_used_check', sql`days_used >= 0`),
  check('bonus_leave_grants_check', sql`days_used <= days_granted`),
]);

